<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="需求到代码五步思维法" id="C5RBs43oDa-KdzZeNtuy">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="从需求到代码的五步思维法" style="rounded=1;whiteSpace=wrap;html=1;fontSize=20;fontStyle=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="450" y="30" width="270" height="60" as="geometry" />
        </mxCell>
        
        <!-- 第一步：明确目标与边界 -->
        <mxCell id="step1" value="第一步：明确目标与边界&#xa;Define the Goal &amp; Boundaries" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="80" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="step1-details" value="• 核心功能是什么？&#xa;• 输入是什么？&#xa;• 输出是什么？&#xa;• 边界是什么？&#xa;  - 环境限制&#xa;  - 数据持久性&#xa;  - 用户范围" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="80" y="250" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 第二步：拆解问题 -->
        <mxCell id="step2" value="第二步：拆解问题&#xa;Decompose the Problem" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="320" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="step2-details" value="• 功能一：添加记录&#xa;• 功能二：列出记录&#xa;• 功能三：显示汇总&#xa;• 功能四：主流程控制&#xa;&#xa;将大问题切成小块&#xa;每个功能独立可实现" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="320" y="250" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 第三步：设计数据模型 -->
        <mxCell id="step3" value="第三步：设计数据模型&#xa;Design the Data Model" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="560" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="step3-details" value="• 核心事物是什么？&#xa;• 事物有哪些属性？&#xa;• 用Go怎么表示？&#xa;  - struct 定义&#xa;  - slice 容器&#xa;&#xa;先有名词，再有动词" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="560" y="250" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 第四步：设计逻辑流程 -->
        <mxCell id="step4" value="第四步：设计逻辑流程&#xa;Design the Logic Flow" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="800" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="step4-details" value="• 功能 → 函数映射&#xa;• 函数签名设计&#xa;• 调用关系梳理&#xa;• 考虑方法(Method)&#xa;&#xa;串起所有动作" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="800" y="250" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 第五步：编码与迭代 -->
        <mxCell id="step5" value="第五步：编码与迭代&#xa;Code &amp; Iterate" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="450" y="420" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="step5-details" value="• 实现MVP(最小可行产品)&#xa;• 逐步迭代添加功能&#xa;• 重构与优化&#xa;&#xa;不要一次性写完！&#xa;让程序先跑起来" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f8cecc;strokeColor=#b85450;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="450" y="520" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="step1" target="step2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="190" as="sourcePoint" />
            <mxPoint x="310" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="step2" target="step3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="190" as="sourcePoint" />
            <mxPoint x="550" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="step3" target="step4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="770" y="190" as="sourcePoint" />
            <mxPoint x="790" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="step4" target="step5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="900" y="240" as="sourcePoint" />
            <mxPoint x="550" y="410" as="targetPoint" />
            <Array as="points">
              <mxPoint x="900" y="390" />
              <mxPoint x="550" y="390" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 示例项目框 -->
        <mxCell id="example-title" value="示例：命令行个人记账本" style="rounded=1;whiteSpace=wrap;html=1;fontSize=16;fontStyle=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="80" y="680" width="920" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="example-step1" value="目标：命令行记账工具&#xa;输入：键盘命令&#xa;输出：屏幕显示&#xa;边界：单用户，内存存储" style="rounded=0;whiteSpace=wrap;html=1;fontSize=10;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="80" y="730" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="example-step2" value="功能拆解：&#xa;• add - 添加账目&#xa;• list - 列出账目&#xa;• summary - 显示汇总&#xa;• exit - 退出程序" style="rounded=0;whiteSpace=wrap;html=1;fontSize=10;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="270" y="730" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="example-step3" value="数据模型：&#xa;type Transaction struct {&#xa;  ID int&#xa;  Type string&#xa;  Amount float64&#xa;  Description string&#xa;}" style="rounded=0;whiteSpace=wrap;html=1;fontSize=10;fillColor=#fff2cc;strokeColor=#d6b656;align=left;fontFamily=Courier New;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="460" y="730" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="example-step4" value="函数设计：&#xa;• addTransaction()&#xa;• listTransactions()&#xa;• showSummary()&#xa;• main() 主循环" style="rounded=0;whiteSpace=wrap;html=1;fontSize=10;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="650" y="730" width="180" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="example-step5" value="迭代开发：&#xa;1. MVP: add + list&#xa;2. 增加 summary&#xa;3. 增加 delete&#xa;4. 重构优化" style="rounded=0;whiteSpace=wrap;html=1;fontSize=10;fillColor=#fff2cc;strokeColor=#d6b656;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="840" y="730" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- 关键提示 -->
        <mxCell id="tips" value="💡 关键提示" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1050" y="150" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="tips-content" value="• 不要试图一次性完成&#xa;• 每一步都要产出具体内容&#xa;• 卡住时及时寻求帮助&#xa;• 从MVP开始，逐步迭代&#xa;• 重构是必要的过程" style="rounded=0;whiteSpace=wrap;html=1;fontSize=11;fillColor=#dae8fc;strokeColor=#6c8ebf;align=left;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1050" y="200" width="100" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
