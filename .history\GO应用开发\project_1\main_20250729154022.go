package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 用户创建后，发送欢迎邮件
var email<PERSON>han chan string

// 用户结构体
type User struct {
	ID    uint   `json:"id" gorm:"primaryKey"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

// 文章结构体
type Post struct {
	ID      uint   `json:"id" gorm:"primaryKey" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  uint   `json:"user_id"` //外键
}

type Server struct {
	db *gorm.DB
}

// 初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查User和Post结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&User{}, &Post{})
	if err != nil {
		return nil, err
	}
	return db, err
}

type NotFoundError struct {
	Field string
}

// 自定义错误
func (nfe *NotFoundError) Error() string {
	return fmt.Sprintf("%s not found", nfe.Field)
}

// notfound 错误处理函数
func notfound(c *gin.Context, resource string) {
	err := &NotFoundError{Field: resource}
	c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
}

// emailService 是一个后台goroutine，负责处理邮件发送任务
func emailService(emailChan <-chan string) {
	log.Println("邮件服务已启动...")
	// 使用单goroutine顺序处理邮件，保证资源稳定
	for email := range emailChan {
		sendWelcomeEmail(email)
	}
	log.Println("邮件服务已关闭。")
}

// sendWelcomeEmail 模拟发送邮件的耗时操作
func sendWelcomeEmail(email string) {
	log.Printf("[Email Service] 正在向 %s 发送欢迎邮件...\n", email)
	time.Sleep(2 * time.Second) // 模拟网络延迟
	log.Printf("[Email Service] 欢迎邮件已成功发送至 %s\n", email)
}

// 创建用户处理器
func (s *Server) createUserHandler(c *gin.Context) {
	var newUser User
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newUser 结构体中
	err := c.ShouldBindJSON(&newUser)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newUser)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	emailChan <- newUser.Email
	c.JSON(http.StatusCreated, newUser)
}

// 获取所有用户处理器
func (s *Server) getAllUserHandler(c *gin.Context) {
	var users []User
	result := s.db.Find(&users)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, users)
}

// 获取单一用户处理器
func (s *Server) getUserHandler(c *gin.Context) {
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "user")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, user)
}

// 创建文章处理器
func (s *Server) createPostHandler(c *gin.Context) {
	var newPost Post
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newPost 结构体中
	err := c.ShouldBindJSON(&newPost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//增加用户存在性校验
	var user User
	if err := s.db.First(&user, newPost.UserID).Error; err != nil {
		notfound(c, "user")
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newPost)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusCreated, newPost)
}

// 获取所有文章处理器
func (s *Server) getAllPostHandler(c *gin.Context) {
	var posts []Post
	result := s.db.Find(&posts)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, posts)
}

// 获取单一文章处理器
func (s *Server) getPostHandler(c *gin.Context) {
	var post Post
	id := c.Params.ByName("id")
	result := s.db.First(&post, id)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "post")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, post)
}

// 获取某个用户的所有文章
func (s *Server) getUserPostsHandler(c *gin.Context) {
	id := c.Params.ByName("id")

	// 首先检查用户是否存在
	var user User
	userResult := s.db.First(&user, id)
	if userResult.Error == gorm.ErrRecordNotFound {
		notfound(c, "user")
		return
	} else if userResult.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": userResult.Error.Error()})
		return
	}

	// 用户存在，查询该用户的文章
	var posts []Post
	result := s.db.Where("user_id = ?", id).Find(&posts)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, posts)
}

// 全局邮件通道
var emailChan = make(chan string, 100)

func main() {
	// 启动邮件发送goroutine
	go emailService(emailChan)

	//初始化并创建数据库
	db, err := initGormDB("blog.db")
	if err != nil {
		fmt.Println("failed to connect database")
		panic(err)
	}
	// 创建一个Server实例
	server := &Server{db: db}
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册路由
	//用户路由组
	userRoutes := r.Group("/users")
	{
		userRoutes.POST("", server.createUserHandler)
		userRoutes.GET("", server.getAllUserHandler)
		userRoutes.GET("/:id", server.getUserHandler)
		userRoutes.GET("/:id/posts", server.getUserPostsHandler)
	}
	//文章路由组
	postRoutes := r.Group("/posts")
	{
		postRoutes.POST("", server.createPostHandler)
		postRoutes.GET("", server.getAllPostHandler)
		postRoutes.GET("/:id", server.getPostHandler)
	}
	// 启动服务器
	r.Run(":8080")
}
