## 项目目标
你的任务是使用Gin和GORM，构建一个简单的博客系统后端API。这个API需要管理“用户（Users）”和他们发布的“文章（Posts）”两种资源，并且在用户注册后，能**并发地**模拟发送一封欢迎邮件。

## 技术栈
* **Web框架**: Gin
* **数据库交互**: GORM
* **数据库**: SQLite

## 目标与边界
### 核心功能
#### 管理员
- 管理用户
- 管理文章
- 发送欢迎邮件

#### 用户
- 注册
- 登录
- 发布文章
- 查看文章
- 查看个人资料
- 修改个人资料

### 输入
- 用户通过HTTP请求与API交互
- 用户注册时提供姓名和邮箱
- 发布文章时提供标题和内容

### 输出
- API响应HTTP请求
- 注册成功时发送欢迎邮件
- 文章发布成功时返回文章详情

### 边界
- 环境：Web服务器
- 数据持久性：使用SQLite数据库存储用户和文章数据
- 用户: 多用户使用

### 产出
我要做一个博客系统，用户可以注册、发布文章，管理员可以管理用户和文章。


## 需求与功能分解
### 1. 数据模型
你需要定义两个核心的`struct`作为GORM的模型：
* **`User`**:
    * `ID` (int, 主键, 自增) 
    * `Name` (string)
    * `Email` (string, 应该是唯一的)

* **`Post`**:
    * `ID` (int, 主键, 自增)
    * `Title` (string)
    * `Content` (string)
    * `UserID` (int) - 这个字段是“外键”，用来关联文章和它的作者。

### 2. 数据库设置
你的`initGormDB`函数需要能够使用`AutoMigrate`同时初始化**两张表**：`users` 和 `posts`。

### 3. API 路由设计
你需要实现以下RESTful API端点：

**用户管理 (User Endpoints):**

* `POST /users`
    * **功能**: 创建一个新用户。
    * **请求体**: 包含`name`和`email`的JSON。
    * **成功响应**: `201 Created`，返回新创建的用户信息（包含ID）。
* `GET /users`
    * **功能**: 获取所有用户的列表。
    * **成功响应**: `200 OK`，返回一个包含所有用户的**`slice`**的JSON。
* `GET /users/:id`
    * **功能**: 根据ID获取单个用户的信息。
    * **成功响应**: `200 OK`，返回该用户的JSON。
    * **失败响应**: 如果用户不存在，返回`404 Not Found`。

**文章管理 (Post Endpoints):**

* `POST /posts`
    * **功能**: 创建一篇新文章。
    * **请求体**: 包含`title`, `content`, 和 `user_id`的JSON。
    * **成功响应**: `201 Created`，返回新创建的文章信息。
    * **失败响应**: 如果`user_id`对应的用户不存在，返回`400 Bad Request`。
* `GET /posts`
    * **功能**: 获取所有文章的列表。
    * **成功响应**: `200 OK`，返回一个文章**`slice`**的JSON。
* `GET /posts/:id`
    * **功能**: 根据ID获取单篇文章。
    * **成功响应**: `200 OK`，返回该文章的JSON。
    * **失败响应**: 如果文章不存在，返回`404 Not Found`。
* `GET /users/:id/posts`
    * **功能**: 获取**某个特定用户**发布的所有文章。
    * **成功响应**: `200 OK`，返回该用户所有文章的**`slice`**。
    * **失败响应**: 如果用户不存在，返回`404 Not Found`。
    * **注意**: 这个端点需要使用GORM的预加载功能，确保返回的文章数据中包含作者的姓名。

### 4. 错误处理
* 鼓励你为“资源未找到”的情况定义一个通用的自定义错误，并在处理器中返回它。
* 确保所有的数据库操作都进行了错误检查。

### 5. 并发巩固挑战：后台邮件通知
* **需求**：当一个新用户通过`POST /users`成功创建并存入数据库后，API应该**立即**向客户端返回`201 Created`的成功响应。**与此同时**，程序需要在**后台**模拟发送一封欢迎邮件给这位新用户。

* **实现思路**：
    1.  在`main`函数中，创建一个**`channel`**，这个channel专门用来传递需要发送邮件的“邮箱地址”（字符串类型）。
    2.  在`main`函数中，启动一个**单独的`goroutine`**。这个goroutine的职责是作为“邮件发送工”，它在一个无限循环中等待从邮件`channel`接收地址，一旦收到，就打印一条模拟信息，比如 `"[Email Service] Sending welcome email to: <EMAIL>"`，并`time.Sleep`一小段时间来模拟耗时。
    3.  在你处理`POST /users`的处理器中，当GORM成功创建用户后，**不要**在处理器中直接模拟发送邮件。而是，将新用户的`Email`地址，作为一个消息**发送到那个全局的`channel`**中。
    4.  发送到channel后，处理器应立即向客户端返回JSON响应，**不等待**“邮件”是否发送成功。

* **注意**：
    * 请确保你的`main`函数中的`goroutine`是**无限循环**的，这样才能持续监听邮件发送任务。
    * 在`POST /users`的处理器中，发送邮件地址到`channel`后，不要有任何`time.Sleep`或其他等待操作。

## 为什么这么设计？
这完美地模拟了真实世界的场景：发送邮件是一个相对缓慢的操作，我们不希望让用户等待这个操作完成才给他响应。通过goroutine和channel，我们将这个“慢操作”异步化了，极大地提升了API的响应速度和用户体验。