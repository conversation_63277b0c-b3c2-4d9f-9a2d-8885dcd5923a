package main

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type Book struct {
	ID     int    `json:"id" gorm:"primaryKey"`
	Title  string `json:"title"`
	Author string `json:"author"`
}

type Server struct {
	db *gorm.DB
}

// initGormDB 初始化并返回一个GORM数据库对象
func initGormDB(filepath string) (*gorm.DB, error) {
	// gorm.Open 会连接数据库
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查Book结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&Book{})
	if err != nil {
		return nil, err
	}
	return db, err
}

// 查询book处理器，处理单条查询
func (s *Server) BookHandler(c *gin.Context) {
	var book Book
	id := c.Params.ByName("id")
	//s.db.First 查询单行数据
	result := s.db.First(&book, id)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(http.StatusNotFound, gin.H{"error": "Book not found"})
		return
	}
	c.JSON(http.StatusOK, book)
}

// 查询book处理器，处理多条查询
func (s *Server) getAllBookHandler(c *gin.Context) {
	var books []Book
	result := s.db.Find(&books)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, books)
}

// 创建book处理器
func (s *Server) createBookHandler(c *gin.Context) {
	var newbook Book
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newBook 结构体中
	if err := c.ShouldBindJSON(&newbook); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	// 替代了我们之前手写的一大段INSERT INTO...的SQL语句。
	// 替代Exec和LastInsertId
	result := s.db.Create(&newbook)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	// 返回 201 Created 状态码和新创建的书籍信息
	c.JSON(http.StatusCreated, newbook)
}

func main() {
	//初始化数据库
	db, err := initGormDB("./books.db")
	if err != nil {
		panic("数据库初始化失败: %v", err)
		// panic("failed to connect database")
	}
	//创建server实例
	server := &Server{db: db}
	//创建gin引擎
	router := gin.Default()
	//注册路由
	router.GET("/books/:id", server.BookHandler)
	router.GET("/books", server.getAllBookHandler)
	router.POST("/books", server.createBookHandler)
	//启动服务器
	router.Run(":8080")
}
