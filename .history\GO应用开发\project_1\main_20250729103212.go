package main

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 用户结构体
type User struct {
	ID    int    `json:"id" gorm:"primaryKey autoIncrement"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

// 文章结构体
type Post struct {
	ID      int    `json:"id" gorm:"primaryKey autoIncrement" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  int    `json:"user_id" gorm:"foreignKey"` //外键
}

type Server struct {
	db *gorm.DB
}

// 初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查User和Post结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&User{}, &Post{})
	if err != nil {
		return nil, err
	}
	return db, err
}

type NotFoundError struct {
	Field string
}

// 自定义错误
func (nfe *NotFoundError) Error() string {
	return fmt.Sprintf("error: %s not found", nfe.Field)
}

// notfound 错误处理函数
func notfound(c *gin.Context, resource string) {
	c.JSON(http.StatusNotFound, gin.H{"error": &NotFoundError{Field: resource}})
}

// 创建用户处理器
func (s *Server) createUserHandler(c *gin.Context) {
	var newUser User
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newUser 结构体中
	err := c.ShouldBindJSON(&newUser)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newUser)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusCreated, newUser)
}

// 获取所有用户处理器
func (s *Server) getAllUserHandler(c *gin.Context) {
	var users []User
	result := s.db.Find(&users)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "users")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, users)
}

// 获取单一用户处理器
func (s *Server) getUserHandler(c *gin.Context) {
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "user")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, user)
}

// 创建文章处理器
func (s *Server) createPostHandler(c *gin.Context) {
	var newPost Post
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newPost 结构体中
	err := c.ShouldBindJSON(&newPost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newPost)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusCreated, newPost)
}

// 获取所有文章处理器
func (s *Server) getAllPostHandler(c *gin.Context) {
	var posts []Post
	result := s.db.Find(&posts)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "posts")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, posts)
}

// 获取单一文章处理器
func (s *Server) getPostHandler(c *gin.Context) {
	var post Post
	id := c.Params.ByName("id")
	result := s.db.First(&post, id)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "post")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, post)
}

// 获取某个用户的所有文章
func (s *Server) getUserPostsHandler(c *gin.Context) {
	var posts []Post
	id := c.Params.ByName("id")
	result := s.db.Where("user_id = ?", id).Find(&posts)
	if result.Error == gorm.ErrRecordNotFound {
		notfound(c, "user posts")
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	// 检查是否找到文章，如果没有找到则返回404
	if len(posts) == 0 {
		notfound(c, "user posts")
		return
	}
	c.JSON(http.StatusOK, posts)
}

func main() {
	db, err := initGormDB("blog.db")
	if err != nil {
		fmt.Println("failed to connect database")
		panic(err)
	}
	// 创建一个Server实例
	server := &Server{db: db}
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册路由
	r.POST("/users", server.createUserHandler)
	r.GET("/users", server.getAllUserHandler)
	r.GET("/users/:id", server.getUserHandler)
	r.POST("/posts", server.createPostHandler)
	r.GET("/posts", server.getAllPostHandler)
	r.GET("/posts/:id", server.getPostHandler)
	r.GET("/users/:id/posts", server.getUserPostsHandler)
	// 启动服务器
	r.Run(":8080")
}
