## 项目目标
你的任务是使用Gin和GORM，构建一个简单的博客系统后端API。这个API需要管理“用户（Users）”和他们发布的“文章（Posts）”两种资源，并且在用户注册后，能**并发地**模拟发送一封欢迎邮件。

## 技术栈
* **Web框架**: Gin
* **数据库交互**: GORM
* **数据库**: SQLite

## 目标与边界
### 核心功能
- 管理用户
- 管理文章
- 发送欢迎邮件

### 输入
- 用户通过HTTP请求与API交互
- 用户注册时提供姓名和邮箱
- 发布文章时提供标题和内容

### 输出
- API响应HTTP请求
- 注册成功时发送欢迎邮件
- 文章发布成功时返回文章详情

### 边界
- 环境：Web服务器
- 数据持久性：使用SQLite数据库存储用户和文章数据


## 需求与功能分解
### 1. 数据模型
你需要定义两个核心的`struct`作为GORM的模型：
* **`User`**:
    * `ID` (int, 主键)
    * `Name` (string)
    * `Email` (string, 应该是唯一的)
