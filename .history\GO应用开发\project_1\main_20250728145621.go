package main

import (
	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 用户结构体
type User struct {
	ID    int    `json:"id" gorm:"primaryKey autoIncrement"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

// 文章结构体
type Post struct {
	ID      int    `json:"id" gorm:"primaryKey" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  int    `json:"user_id" gorm:"foreignKey"` //外键
}

type Server struct {
	db *gorm.DB
}

// 初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查User和Post结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&User{}, &Post{})
	if err != nil {
		return nil, err
	}
	return db, err
}

func main() {
	db, err := initGormDB("blog.db")
	if err != nil {
		panic("failed to connect database")
	}
	// 创建一个Server实例
	server := &Server{db: db}
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册路由
	r.POST("/users", server.createUserHandler)
	// 启动服务器
	r.Run()
}
