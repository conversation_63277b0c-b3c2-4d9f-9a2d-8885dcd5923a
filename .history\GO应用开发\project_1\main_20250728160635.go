package main

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 用户结构体
type User struct {
	ID    int    `json:"id" gorm:"primaryKey autoIncrement"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

// 文章结构体
type Post struct {
	ID      int    `json:"id" gorm:"primaryKey" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  int    `json:"user_id" gorm:"foreignKey"` //外键
}

type Server struct {
	db *gorm.DB
}

// 初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查User和Post结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&User{}, &Post{})
	if err != nil {
		return nil, err
	}
	return db, err
}

// 创建用户处理器
func (s *Server) createUserHandler(c *gin.Context) {
	var newUser User
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newUser 结构体中
	err := c.ShouldBindJSON(&newUser)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newUser)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusCreated, newUser)
}

// 获取所有用户处理器
func (s *Server) getAllUserHandler(c *gin.Context) {
	var users []User
	result := s.db.Find(&users)
	if result.Error == gorm.ErrRecordNotFound {
		c.JSON(http.StatusNotFound, gin.H{"error": "No users found"})
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, users)
}

// 获取单一用户处理器
func (s *Server) getUserHandler(c *gin.Context) {
	var user User
	id := c.Params.ByName("id")
	result := s.db.First(&user, id)
	if result.Error == gorm.ErrRecordNotFound {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, user)
}

// 创建文章处理器
func (s *Server) createPostHandler(c *gin.Context) {
	var newPost Post
	err := c.ShouldBindJSON(&newPost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	c.JSON(http.StatusOK, newPost)
}

// 获取所有文章处理器
func (s *Server) getAllPostHandler(c *gin.Context) {
	var posts []Post
	result := s.db.Find(&posts)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	c.JSON(http.StatusOK, posts)
}

func main() {
	db, err := initGormDB("blog.db")
	if err != nil {
		panic("failed to connect database")
	}
	// 创建一个Server实例
	server := &Server{db: db}
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册路由
	r.POST("/users", server.createUserHandler)
	r.GET("/users", server.getAllUserHandler)
	r.GET("/users/:id", server.getUserHandler)
	// 启动服务器
	r.Run(":8080")
}
