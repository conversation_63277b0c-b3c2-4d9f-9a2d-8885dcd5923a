package main

import "gorm.io/gorm"

//用户结构体
type User struct {
	ID    int    `json:"id" gorm:"primaryKey autoIncrement"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

//文章结构体
type Post struct {
	ID      int    `json:"id" gorm:"primaryKey" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  int    `json:"user_id" gorm:"foreignKey"` //外键
}

type Server struct {
	db *gorm.DB
}

//初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqllite.Open(filepath), &gorm.Config{})
}

func main() {

}
